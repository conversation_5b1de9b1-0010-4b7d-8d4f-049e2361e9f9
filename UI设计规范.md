# 打印端UI设计规范

> **基于7.30重构手册的完整UI设计规范**  
> 本规范严格遵循7.30重构手册中的UI设计要求，确保整个应用的视觉统一性和用户体验一致性。

## 🎯 设计系统核心原则

### 设计理念
- **现代化**: 采用Material Design 3.0设计语言
- **一致性**: 统一的视觉语言和交互模式
- **可访问性**: 符合WCAG 2.1 AA级无障碍标准
- **响应式**: 适配桌面端和移动端设备
- **专业性**: 企业级应用的专业外观

### 参考设计
- **主要参考**: https://modernize-nextjs.adminmart.com/
- **UI组件库**: Material UI (MUI) v5.14.0+
- **设计风格**: 现代化企业级管理系统

---

## 🎨 色彩系统

### 主色调系统
```css
:root {
  /* 主色调 - 基于Material Design */
  --primary-color: #5d87ff;        /* 主色蓝 - 主要按钮、链接、重要元素 */
  --primary-light: #ecf2ff;        /* 主色浅色 - 背景、悬停状态 */
  --primary-dark: #4570ea;         /* 主色深色 - 按钮按下状态 */
  
  /* 辅助色调 */
  --secondary-color: #49beff;      /* 辅助蓝色 - 次要按钮、标签 */
  --accent-color: #13deb9;         /* 强调色 - 特殊标识、成功状态 */
  
  /* 状态颜色 */
  --success-color: #67c23a;        /* 成功绿色 - 成功状态、完成标识 */
  --warning-color: #ffae1f;        /* 警告橙色 - 警告信息、待处理状态 */
  --danger-color: #fa896b;         /* 危险色 - 错误信息、删除操作 */
  --info-color: #539bff;           /* 信息蓝色 - 提示信息 */

  /* 文字颜色层级 */
  --text-primary: #2a3547;         /* 主要文字 - 标题、重要内容 */
  --text-regular: #5a6a85;         /* 常规文字 - 正文内容 */
  --text-secondary: #7c8fac;       /* 次要文字 - 辅助信息 */
  --text-placeholder: #adb5bd;     /* 占位文字 - 输入框提示 */

  /* 背景颜色系统 */
  --bg-color: #ffffff;             /* 主背景 - 卡片、弹窗背景 */
  --bg-page: #f5f5f9;              /* 页面背景 - 主内容区背景 */
  --bg-light: #f9f9fd;             /* 浅色背景 - 表头、工具栏 */
  --bg-overlay: rgba(0, 0, 0, 0.8); /* 遮罩背景 - 弹窗遮罩 */

  /* 边框颜色 */
  --border-base: #e5eaef;          /* 基础边框 - 输入框、分割线 */
  --border-light: #f1f5f9;         /* 浅色边框 - 表格边框 */
  --border-lighter: #f8fafc;       /* 最浅边框 - 卡片边框 */

  /* 阴影系统 - 与prototype.html完全一致 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 4px rgba(0, 0, 0, 0.08);
  --shadow-light: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-hover: 0 4px 20px rgba(93, 135, 255, 0.15);
  --shadow-card: 0 0 2px rgba(145, 158, 171, 0.2), 0 12px 24px -4px rgba(145, 158, 171, 0.12);

  /* 圆角系统 - 与prototype.html完全一致 */
  --border-radius-small: 4px;
  --border-radius-base: 7px;
  --border-radius-large: 12px;
}
```

### 色彩使用规范
- **主色**: 用于主要操作按钮、导航激活状态、重要链接
- **辅助色**: 用于次要按钮、标签、图标
- **成功色**: 用于成功提示、完成状态、确认操作
- **警告色**: 用于警告信息、待处理状态、注意事项
- **危险色**: 用于错误信息、删除操作、危险警告
- **信息色**: 用于一般信息提示、帮助说明

---

## 🔤 字体系统

### 字体族
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
```

### 字体大小层级
```css
/* 标题字体 */
--font-size-h1: 32px;    /* 主标题 */
--font-size-h2: 28px;    /* 二级标题 */
--font-size-h3: 24px;    /* 三级标题 */
--font-size-h4: 20px;    /* 四级标题 */
--font-size-h5: 18px;    /* 五级标题 */
--font-size-h6: 16px;    /* 六级标题 */

/* 正文字体 */
--font-size-large: 16px;  /* 大号正文 */
--font-size-base: 14px;   /* 基础正文 */
--font-size-small: 12px;  /* 小号文字 */
--font-size-xs: 10px;     /* 极小文字 */

/* 字重 */
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;

/* 行高 */
--line-height-tight: 1.2;
--line-height-normal: 1.5;
--line-height-relaxed: 1.75;
```

---

## 📏 间距系统

### 标准间距
```css
/* 间距系统 - 基于8px网格 */
--spacing-xs: 4px;       /* 极小间距 */
--spacing-sm: 8px;       /* 小间距 */
--spacing-md: 16px;      /* 中等间距 */
--spacing-lg: 24px;      /* 大间距 */
--spacing-xl: 32px;      /* 超大间距 */
--spacing-2xl: 48px;     /* 特大间距 */

/* 组件内边距 */
--padding-xs: 8px 12px;   /* 小按钮 */
--padding-sm: 12px 16px;  /* 中按钮 */
--padding-md: 16px 24px;  /* 大按钮 */
--padding-lg: 20px 32px;  /* 超大按钮 */

/* 容器间距 */
--container-padding: 24px;
--section-margin: 32px;
--card-padding: 24px;
```

### 间距使用规范
- **组件内部**: 8px、12px、16px
- **组件之间**: 16px、24px、32px
- **页面布局**: 24px、32px、48px
- **输入框**: 12px 16px
- **卡片**: 24px
- **表格单元格**: 16px 20px
- **弹窗**: 24px
- **侧边栏**: 280px宽度，内边距24px
- **顶部导航栏**: 80px高度，左右内边距32px

---

## 🧩 组件设计规范

### 侧边导航栏组件
```css
/* 侧边导航栏 - 与prototype.html完全一致 */
.sidebar {
  width: 280px;
  background: var(--bg-color);
  border-right: 1px solid var(--border-light);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1001;
  transition: all 0.3s ease;
}

.sidebar-header {
  padding: 32px 24px 24px;
  border-bottom: 1px solid var(--border-lighter);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: var(--border-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.logo-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  margin-left: 52px;
}

.nav-menu {
  padding: 24px 16px;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-section-title {
  font-size: 11px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  padding: 0 12px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  cursor: pointer;
  border-radius: var(--border-radius-small);
  transition: all 0.2s ease;
  position: relative;
  color: var(--text-regular);
}

.nav-item:hover {
  background: var(--border-lighter);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--primary-light);
  color: var(--primary-color);
  font-weight: 500;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 0 2px 2px 0;
}

.nav-item .material-icons-round {
  margin-right: 12px;
  font-size: 20px;
}

.nav-item span {
  font-size: 14px;
  font-weight: 500;
}
```

### 顶部导航栏组件
```css
/* 顶部导航栏 - 与prototype.html完全一致 */
.app-bar {
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  height: 80px;
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  padding: 0 32px;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-separator {
  font-size: 12px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-top: 4px;
}

.app-bar .spacer {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  position: relative;
}

.search-input {
  width: 300px;
  padding: 12px 16px 12px 44px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-base);
  background: var(--bg-page);
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 18px;
}

.notification-btn, .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-btn {
  background: var(--bg-page);
  color: var(--text-secondary);
  position: relative;
}

.notification-btn:hover {
  background: var(--border-light);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: var(--danger-color);
  border-radius: 50%;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}
```

### 按钮组件
```css
/* 按钮基础样式 - 与prototype.html完全一致 */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

/* 按钮光泽效果 */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

/* 主要按钮 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(93, 135, 255, 0.3);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(93, 135, 255, 0.4);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--bg-page);
  color: var(--text-regular);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--border-lighter);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* 成功按钮 */
.btn-success {
  background: var(--success-color);
  color: white;
  box-shadow: 0 2px 8px rgba(19, 222, 185, 0.3);
}

.btn-success:hover {
  background: #0fc9a7;
  transform: translateY(-1px);
}

/* 警告按钮 */
.btn-warning {
  background: var(--warning-color);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 174, 31, 0.3);
}

.btn-warning:hover {
  background: #e6941a;
  transform: translateY(-1px);
}

/* 危险按钮 */
.btn-danger {
  background: var(--danger-color);
  color: white;
  box-shadow: 0 2px 8px rgba(250, 137, 107, 0.3);
}

.btn-danger:hover {
  background: #f76f4e;
  transform: translateY(-1px);
}
```

### 输入框组件
```css
.input-field {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-base);
  border-radius: 8px;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background: var(--bg-color);
  transition: all 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.input-field::placeholder {
  color: var(--text-placeholder);
}

.input-field:disabled {
  background: var(--bg-light);
  color: var(--text-secondary);
  cursor: not-allowed;
}
```

### 统计卡片组件
```css
/* 统计卡片网格 - 与prototype.html完全一致 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  padding: 24px;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

/* 统计卡片主题色彩 */
.stat-card.orders { --card-color: var(--primary-color); --card-bg: var(--primary-light); }
.stat-card.files { --card-color: var(--info-color); --card-bg: #e7f3ff; }
.stat-card.printing { --card-color: var(--warning-color); --card-bg: #fef5e7; }
.stat-card.completed { --card-color: var(--success-color); --card-bg: #e6fffa; }

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--card-color);
  background: var(--card-bg);
  font-size: 20px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up { color: var(--success-color); }
.stat-trend.down { color: var(--danger-color); }

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}
```

### 卡片组件
```css
/* 卡片样式 - 与prototype.html完全一致 */
.card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-light);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-hover);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-light);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-content {
  padding: 24px;
}
```

### 表格组件
```css
/* 表格样式 - 与prototype.html完全一致 */
.table-container {
  overflow: hidden;
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-lighter);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-color);
}

.table th {
  padding: 16px 20px;
  text-align: left;
  background: var(--bg-light);
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border-bottom: 1px solid var(--border-light);
}

.table td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-regular);
  font-size: 13px;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background: var(--bg-light);
}

.table tbody tr:last-child td {
  border-bottom: none;
}
```

### 状态标签组件
```css
/* 状态标签 - 与prototype.html完全一致 */
.status-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-chip::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-chip.pending {
  background: rgba(255, 174, 31, 0.1);
  color: var(--warning-color);
}
.status-chip.processing {
  background: rgba(93, 135, 255, 0.1);
  color: var(--primary-color);
}
.status-chip.printing {
  background: rgba(83, 155, 255, 0.1);
  color: var(--info-color);
}
.status-chip.completed {
  background: rgba(19, 222, 185, 0.1);
  color: var(--success-color);
}
```

### 工具栏组件
```css
/* 工具栏 - 与prototype.html完全一致 */
.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px 0;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-small);
  background: var(--bg-color);
  color: var(--text-regular);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}
```

---

## 📱 响应式设计

### 断点系统
```css
/* 响应式设计 - 与prototype.html完全一致 */

/* 中等屏幕 */
@media (max-width: 1200px) {
  .sidebar {
    width: 260px;
  }

  .app-bar {
    left: 260px;
  }

  .main-content {
    margin-left: 260px;
  }
}

/* 移动端 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .app-bar {
    left: 0;
    padding: 0 16px;
  }

  .main-content {
    margin-left: 0;
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .search-input {
    width: 200px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-section {
    justify-content: space-between;
  }
}
```

### 布局网格
```css
/* 主布局结构 - 与prototype.html完全一致 */
.app-container {
  display: flex;
  min-height: 100vh;
}

.main-content {
  margin-left: 280px;
  margin-top: 80px;
  padding: 32px;
  flex: 1;
  min-height: calc(100vh - 80px);
}

.grid-container {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.flex-container {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
```

---

## 🎭 状态样式

### 订单状态颜色
```css
.status-pending { 
  background: rgba(255, 174, 31, 0.1); 
  color: var(--warning-color); 
}

.status-processing { 
  background: rgba(93, 135, 255, 0.1); 
  color: var(--primary-color); 
}

.status-completed { 
  background: rgba(19, 222, 185, 0.1); 
  color: var(--success-color); 
}

.status-failed { 
  background: rgba(250, 137, 107, 0.1); 
  color: var(--danger-color); 
}
```

### 交互状态
```css
/* 悬停效果 */
.interactive:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 激活状态 */
.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 禁用状态 */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
```

---

## 🎬 动画效果

### 页面加载动画
```css
/* 页面加载动画 - 与prototype.html完全一致 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}
```

### 按钮交互动画
```css
/* 按钮波纹效果 - 与prototype.html完全一致 */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}
```

### 加载动画
```css
/* 加载动画 - 与prototype.html完全一致 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

---

## 🎯 JavaScript交互功能

### 导航菜单交互
```javascript
// 导航菜单交互 - 与prototype.html完全一致
document.querySelectorAll('.nav-item').forEach(item => {
  item.addEventListener('click', function() {
    document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
    this.classList.add('active');

    // 更新页面标题和面包屑
    const title = this.querySelector('span:last-child').textContent;
    document.querySelector('.page-title').textContent = title;
    document.querySelector('.breadcrumb-item:last-child span').textContent = title;

    // 添加页面切换动画
    const mainContent = document.querySelector('.main-content');
    mainContent.style.opacity = '0.7';
    setTimeout(() => {
      mainContent.style.opacity = '1';
    }, 150);
  });
});
```

### 搜索功能
```javascript
// 搜索功能增强 - 与prototype.html完全一致
const searchInput = document.querySelector('.search-input');
if (searchInput) {
  searchInput.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      if (text.includes(searchTerm)) {
        row.style.display = '';
        row.style.animation = 'fadeInUp 0.3s ease-out';
      } else {
        row.style.display = 'none';
      }
    });
  });
}
```

### 按钮波纹效果
```javascript
// 按钮点击效果 - 与prototype.html完全一致
document.querySelectorAll('.btn').forEach(btn => {
  btn.addEventListener('click', function(e) {
    // 创建波纹效果
    const ripple = document.createElement('span');
    const rect = this.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;

    this.style.position = 'relative';
    this.style.overflow = 'hidden';
    this.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  });
});
```

### 滚动动画观察器
```javascript
// 统计卡片动画 - 与prototype.html完全一致
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.animation = 'fadeInUp 0.6s ease-out';
    }
  });
}, observerOptions);

document.querySelectorAll('.stat-card, .card').forEach(card => {
  observer.observe(card);
});
```

---

## 📋 Material UI 组件配置

### 主题配置
```typescript
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#5d87ff',
      light: '#ecf2ff',
      dark: '#4570ea',
    },
    secondary: {
      main: '#49beff',
    },
    success: {
      main: '#67c23a',
    },
    warning: {
      main: '#ffae1f',
    },
    error: {
      main: '#fa896b',
    },
    info: {
      main: '#539bff',
    },
    background: {
      default: '#f5f5f9',
      paper: '#ffffff',
    },
    text: {
      primary: '#2a3547',
      secondary: '#5a6a85',
    },
  },
  typography: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    h1: { fontSize: '32px', fontWeight: 600 },
    h2: { fontSize: '28px', fontWeight: 600 },
    h3: { fontSize: '24px', fontWeight: 600 },
    h4: { fontSize: '20px', fontWeight: 600 },
    h5: { fontSize: '18px', fontWeight: 600 },
    h6: { fontSize: '16px', fontWeight: 600 },
    body1: { fontSize: '14px', lineHeight: 1.5 },
    body2: { fontSize: '12px', lineHeight: 1.5 },
  },
  shape: {
    borderRadius: 8,
  },
  shadows: [
    'none',
    '0 1px 2px rgba(0, 0, 0, 0.05)',
    '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
    // ... 更多阴影级别
  ],
});
```

---

## 🏗️ 页面布局规范

### 主布局结构
```typescript
// 主布局组件
const MainLayout = () => {
  return (
    <Box sx={{ display: 'flex' }}>
      {/* 顶部导航栏 */}
      <AppBar position="fixed" sx={{ zIndex: 1201 }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            云打印管理系统
          </Typography>
          <IconButton color="inherit">
            <NotificationsIcon />
          </IconButton>
          <IconButton color="inherit">
            <AccountCircleIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 侧边导航菜单 */}
      <Drawer
        variant="permanent"
        sx={{
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            marginTop: '64px', // AppBar高度
          },
        }}
      >
        <List>
          <ListItem button>
            <ListItemIcon><DashboardIcon /></ListItemIcon>
            <ListItemText primary="仪表盘" />
          </ListItem>
          <ListItem button>
            <ListItemIcon><AssignmentIcon /></ListItemIcon>
            <ListItemText primary="订单管理" />
          </ListItem>
          <ListItem button>
            <ListItemIcon><PrintIcon /></ListItemIcon>
            <ListItemText primary="打印队列" />
          </ListItem>
          <ListItem button>
            <ListItemIcon><LocalShippingIcon /></ListItemIcon>
            <ListItemText primary="物流发货" />
          </ListItem>
          <ListItem button>
            <ListItemIcon><SettingsIcon /></ListItemIcon>
            <ListItemText primary="系统设置" />
          </ListItem>
        </List>
      </Drawer>

      {/* 主内容区 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: 'background.default',
          p: 3,
          marginTop: '64px', // AppBar高度
          marginLeft: '280px', // Drawer宽度
        }}
      >
        {children}
      </Box>
    </Box>
  );
};
```

### 页面容器规范
```css
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.page-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}
```

---



## ⚠️ 开发规范要求

### 强制性要求
1. **所有新建组件必须使用上述CSS变量系统**
2. **禁止硬编码颜色值，必须使用预定义的CSS变量**
3. **所有按钮、卡片、表格必须遵循统一的样式规范**
4. **响应式设计必须支持移动端、平板端、桌面端三种布局**
5. **动画效果必须使用统一的缓动函数和时长**
6. **数据结构必须严格按照7.30重构手册中的接口定义实现**
7. **订单状态流转必须遵循预定义的状态机**
8. **必须完全按照prototype.html的视觉效果和交互行为实现**
9. **侧边栏宽度固定280px，顶部导航栏高度固定80px**
10. **所有阴影、圆角、间距必须与prototype.html保持一致**

### 数据一致性要求
1. **所有时间显示必须使用统一格式 (YYYY-MM-DD HH:mm:ss)**
2. **金额显示必须保留两位小数，使用千分位分隔符**
3. **时间格式统一使用ISO 8601标准**
4. **价格字段统一使用字符串类型，保留两位小数**
5. **文件大小统一使用字节为单位，显示时转换**

### 代码审查检查点
- [ ] 是否使用了CSS变量而非硬编码颜色
- [ ] 组件样式是否符合设计规范
- [ ] 响应式布局是否正确实现
- [ ] 动画效果是否流畅自然
- [ ] 无障碍性是否符合标准
- [ ] 数据结构是否符合接口定义
- [ ] 状态管理是否正确实现
- [ ] 错误处理是否完善
- [ ] 是否与prototype.html的视觉效果完全一致
- [ ] 侧边栏和顶部导航栏尺寸是否正确
- [ ] 阴影效果是否与prototype.html一致
- [ ] 按钮交互效果是否包含波纹动画

### 设计一致性验证
- [ ] 色彩搭配是否与prototype.html完全一致
- [ ] 字体大小和间距是否与prototype.html规范一致
- [ ] 组件状态变化是否有适当反馈
- [ ] 整体视觉风格是否与prototype.html统一
- [ ] 数据展示格式是否一致
- [ ] 交互行为是否符合prototype.html的用户体验
- [ ] 统计卡片的主题色彩是否正确应用
- [ ] 表格样式是否与prototype.html完全匹配
- [ ] 状态标签样式是否与prototype.html一致

---

## 🔍 prototype.html完美复原检查清单

### 布局结构检查
- [ ] 侧边栏宽度280px，固定定位
- [ ] 顶部导航栏高度80px，左边距280px
- [ ] 主内容区左边距280px，顶部边距80px
- [ ] 主内容区内边距32px

### 侧边栏检查
- [ ] Logo区域：图标40x40px，渐变背景，圆角4px
- [ ] Logo文字：20px，字重700
- [ ] Logo副标题：12px，左边距52px
- [ ] 导航分组标题：11px，大写，字间距0.5px
- [ ] 导航项：12px内边距，4px圆角，悬停2px右移
- [ ] 激活状态：主色浅色背景，左侧3px蓝色条

### 顶部导航栏检查
- [ ] 面包屑导航：14px字体，8px间距
- [ ] 页面标题：24px，字重700，顶部边距4px
- [ ] 搜索框：300px宽度，44px左内边距，7px圆角
- [ ] 通知按钮：40px圆形，相对定位红点
- [ ] 用户头像：40px圆形，渐变背景

### 统计卡片检查
- [ ] 网格布局：最小280px，24px间距
- [ ] 卡片内边距：24px
- [ ] 卡片阴影：var(--shadow-card)
- [ ] 图标：48px，7px圆角，主题色背景
- [ ] 数字：32px，字重700
- [ ] 趋势指示器：12px，成功/危险色

### 表格检查
- [ ] 表头：16px内边距，11px大写字体，0.8px字间距
- [ ] 表格单元格：16px内边距，13px字体
- [ ] 悬停效果：浅色背景
- [ ] 边框：1px浅色边框

### 按钮检查
- [ ] 基础内边距：12px 20px
- [ ] 圆角：4px
- [ ] 光泽效果：渐变动画
- [ ] 悬停效果：-1px上移，增强阴影
- [ ] 波纹效果：点击时白色半透明圆形扩散

### 状态标签检查
- [ ] 圆角：20px
- [ ] 内边距：6px 12px
- [ ] 前置圆点：6px直径
- [ ] 大写字母：12px，0.5px字间距

### 动画效果检查
- [ ] 页面加载：fadeInUp 0.6s
- [ ] 侧边栏：slideInLeft 0.4s
- [ ] 按钮悬停：0.2s过渡
- [ ] 导航项悬停：2px右移
- [ ] 卡片悬停：-2px上移

### 响应式检查
- [ ] 1200px以下：侧边栏260px
- [ ] 768px以下：侧边栏隐藏，可切换
- [ ] 移动端：搜索框200px，工具栏垂直布局

---

*本规范基于prototype.html完全制定，确保100%视觉还原度*

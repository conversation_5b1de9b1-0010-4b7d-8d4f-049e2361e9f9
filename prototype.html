<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云打印终端 - 现代化界面</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <style>
        :root {
            /* 主色调 - 参考modernize配色 */
            --primary-color: #5d87ff;
            --primary-light: #ecf2ff;
            --primary-dark: #4570ea;
            --secondary-color: #49beff;
            --success-color: #13deb9;
            --warning-color: #ffae1f;
            --danger-color: #fa896b;
            --info-color: #539bff;

            /* 文字颜色 */
            --text-primary: #2a3547;
            --text-regular: #5a6a85;
            --text-secondary: #7c8fac;
            --text-placeholder: #adb5bd;

            /* 背景颜色 */
            --bg-color: #ffffff;
            --bg-page: #f5f5f9;
            --bg-overlay: rgba(0, 0, 0, 0.8);
            --bg-light: #f9f9fd;

            /* 边框颜色 */
            --border-base: #e5eaef;
            --border-light: #f1f5f9;
            --border-lighter: #f8fafc;

            /* 阴影 */
            --shadow-base: 0 1px 4px rgba(0, 0, 0, 0.08);
            --shadow-light: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-hover: 0 4px 20px rgba(93, 135, 255, 0.15);
            --shadow-card: 0 0 2px rgba(145, 158, 171, 0.2), 0 12px 24px -4px rgba(145, 158, 171, 0.12);

            /* 圆角 */
            --border-radius-base: 7px;
            --border-radius-small: 4px;
            --border-radius-large: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-page);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边导航 - 现代化设计 */
        .sidebar {
            width: 280px;
            background: var(--bg-color);
            border-right: 1px solid var(--border-light);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 32px 24px 24px;
            border-bottom: 1px solid var(--border-lighter);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: var(--border-radius-small);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .logo-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
            margin-left: 52px;
        }

        .nav-menu {
            padding: 24px 16px;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            font-size: 11px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
            padding: 0 12px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 4px;
            cursor: pointer;
            border-radius: var(--border-radius-small);
            transition: all 0.2s ease;
            position: relative;
            color: var(--text-regular);
        }

        .nav-item:hover {
            background: var(--border-lighter);
            color: var(--text-primary);
            transform: translateX(2px);
        }

        .nav-item.active {
            background: var(--primary-light);
            color: var(--primary-color);
            font-weight: 500;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }

        .nav-item .material-icons-round {
            margin-right: 12px;
            font-size: 20px;
        }

        .nav-item span {
            font-size: 14px;
            font-weight: 500;
        }

        /* 顶部导航栏 - 现代化设计 */
        .app-bar {
            position: fixed;
            top: 0;
            left: 280px;
            right: 0;
            height: 80px;
            background: var(--bg-color);
            border-bottom: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            padding: 0 32px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-separator {
            font-size: 12px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-top: 4px;
        }

        .app-bar .spacer {
            flex: 1;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius-base);
            background: var(--bg-page);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 18px;
        }

        .notification-btn, .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .notification-btn {
            background: var(--bg-page);
            color: var(--text-secondary);
            position: relative;
        }

        .notification-btn:hover {
            background: var(--border-light);
            color: var(--text-primary);
        }

        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: var(--danger-color);
            border-radius: 50%;
        }

        .user-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 280px;
            margin-top: 80px;
            padding: 32px;
            flex: 1;
            min-height: calc(100vh - 80px);
        }

        /* 统计卡片 - 现代化设计 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--bg-color);
            border-radius: var(--border-radius-base);
            padding: 24px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-light);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .stat-card.orders { --card-color: var(--primary-color); --card-bg: var(--primary-light); }
        .stat-card.files { --card-color: var(--info-color); --card-bg: #e7f3ff; }
        .stat-card.printing { --card-color: var(--warning-color); --card-bg: #fef5e7; }
        .stat-card.completed { --card-color: var(--success-color); --card-bg: #e6fffa; }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-base);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--card-color);
            background: var(--card-bg);
            font-size: 20px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .stat-trend.up { color: var(--success-color); }
        .stat-trend.down { color: var(--danger-color); }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 卡片样式 - 现代化设计 */
        .card {
            background: var(--bg-color);
            border-radius: var(--border-radius-base);
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-light);
            margin-bottom: 24px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: var(--bg-light);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-content {
            padding: 24px;
        }

        /* 按钮样式 - 现代化设计 */
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: var(--border-radius-small);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 8px rgba(93, 135, 255, 0.3);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(93, 135, 255, 0.4);
        }

        .btn-secondary {
            background: var(--bg-page);
            color: var(--text-regular);
            border: 1px solid var(--border-light);
        }

        .btn-secondary:hover {
            background: var(--border-lighter);
            color: var(--text-primary);
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
            box-shadow: 0 2px 8px rgba(19, 222, 185, 0.3);
        }

        .btn-success:hover {
            background: #0fc9a7;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 174, 31, 0.3);
        }

        .btn-warning:hover {
            background: #e6941a;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
            box-shadow: 0 2px 8px rgba(250, 137, 107, 0.3);
        }

        .btn-danger:hover {
            background: #f76f4e;
            transform: translateY(-1px);
        }

        /* 表格样式 - 现代化设计 */
        .table-container {
            overflow: hidden;
            border-radius: var(--border-radius-base);
            border: 1px solid var(--border-lighter);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-color);
        }

        .table th {
            padding: 16px 20px;
            text-align: left;
            background: var(--bg-light);
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            border-bottom: 1px solid var(--border-light);
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-light);
            color: var(--text-regular);
            font-size: 13px;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: var(--bg-light);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 状态标签 - 现代化设计 */
        .status-chip {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-chip::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        .status-chip.pending {
            background: rgba(255, 174, 31, 0.1);
            color: var(--warning-color);
        }
        .status-chip.processing {
            background: rgba(93, 135, 255, 0.1);
            color: var(--primary-color);
        }
        .status-chip.printing {
            background: rgba(83, 155, 255, 0.1);
            color: var(--info-color);
        }
        .status-chip.completed {
            background: rgba(19, 222, 185, 0.1);
            color: var(--success-color);
        }

        /* 工具栏 - 现代化设计 */
        .toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            padding: 20px 0;
        }

        .filter-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filter-select {
            padding: 10px 16px;
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius-small);
            background: var(--bg-color);
            color: var(--text-regular);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .slide-in-left {
            animation: slideInLeft 0.4s ease-out;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 260px;
            }

            .app-bar {
                left: 260px;
            }

            .main-content {
                margin-left: 260px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .app-bar {
                left: 0;
                padding: 0 16px;
            }

            .main-content {
                margin-left: 0;
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .search-input {
                width: 200px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .filter-section {
                justify-content: space-between;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-light);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边导航 -->
        <nav class="sidebar slide-in-left">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <span class="material-icons-round">print</span>
                    </div>
                    <div class="logo-text">云打印终端</div>
                </div>
                <div class="logo-subtitle">Print Terminal v2.0</div>
            </div>
            <div class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">主要功能</div>
                    <div class="nav-item active">
                        <span class="material-icons-round">dashboard</span>
                        <span>仪表盘</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">assignment</span>
                        <span>订单管理</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">description</span>
                        <span>文件处理</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">print</span>
                        <span>打印队列</span>
                    </div>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">业务管理</div>
                    <div class="nav-item">
                        <span class="material-icons-round">local_shipping</span>
                        <span>物流管理</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">sync</span>
                        <span>云端同步</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">analytics</span>
                        <span>数据统计</span>
                    </div>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">系统</div>
                    <div class="nav-item">
                        <span class="material-icons-round">settings</span>
                        <span>系统设置</span>
                    </div>
                    <div class="nav-item">
                        <span class="material-icons-round">help</span>
                        <span>帮助中心</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 顶部导航栏 -->
        <header class="app-bar">
            <div>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <span class="material-icons-round">home</span>
                        <span>首页</span>
                    </div>
                    <span class="breadcrumb-separator">/</span>
                    <div class="breadcrumb-item">
                        <span>仪表盘</span>
                    </div>
                </div>
                <h1 class="page-title">仪表盘</h1>
            </div>
            <div class="spacer"></div>
            <div class="header-actions">
                <div class="search-container">
                    <span class="material-icons-round search-icon">search</span>
                    <input type="text" class="search-input" placeholder="搜索订单、客户...">
                </div>
                <div class="notification-btn">
                    <span class="material-icons-round">notifications</span>
                    <div class="notification-badge"></div>
                </div>
                <div class="user-avatar">
                    <span>A</span>
                </div>
            </div>
        </header>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 统计卡片 -->
            <div class="stats-grid fade-in-up">
                <div class="stat-card orders">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="material-icons-round">assignment</span>
                        </div>
                        <div class="stat-trend up">
                            <span class="material-icons-round">trending_up</span>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="stat-number">156</div>
                    <div class="stat-label">今日订单</div>
                </div>
                <div class="stat-card files">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="material-icons-round">description</span>
                        </div>
                        <div class="stat-trend up">
                            <span class="material-icons-round">trending_up</span>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="stat-number">324</div>
                    <div class="stat-label">处理文件</div>
                </div>
                <div class="stat-card printing">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="material-icons-round">print</span>
                        </div>
                        <div class="stat-trend down">
                            <span class="material-icons-round">trending_down</span>
                            <span>-3%</span>
                        </div>
                    </div>
                    <div class="stat-number">89</div>
                    <div class="stat-label">打印中</div>
                </div>
                <div class="stat-card completed">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="material-icons-round">check_circle</span>
                        </div>
                        <div class="stat-trend up">
                            <span class="material-icons-round">trending_up</span>
                            <span>+15%</span>
                        </div>
                    </div>
                    <div class="stat-number">234</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>

            <!-- 最近订单 -->
            <div class="card fade-in-up">
                <div class="card-header">
                    <h2 class="card-title">最近订单</h2>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-secondary">
                            <span class="material-icons-round">download</span>
                            导出
                        </button>
                        <button class="btn btn-primary">
                            <span class="material-icons-round">add</span>
                            新建订单
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="toolbar">
                        <div class="filter-section">
                            <select class="filter-select">
                                <option>全部状态</option>
                                <option>待处理</option>
                                <option>处理中</option>
                                <option>打印中</option>
                                <option>已完成</option>
                            </select>
                            <select class="filter-select">
                                <option>今天</option>
                                <option>本周</option>
                                <option>本月</option>
                                <option>自定义</option>
                            </select>
                        </div>
                        <div style="flex: 1;"></div>
                        <button class="btn btn-secondary">
                            <span class="material-icons-round">filter_list</span>
                            筛选
                        </button>
                        <button class="btn btn-secondary">
                            <span class="material-icons-round">refresh</span>
                            刷新
                        </button>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>客户信息</th>
                                    <th>文件数量</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: var(--primary-color);">P2024073001</div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 500;">张三</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">138****5678</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <span class="material-icons-round" style="font-size: 16px; color: var(--text-secondary);">description</span>
                                            <span>3个文件</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 600;">¥45.80</div>
                                    </td>
                                    <td><span class="status-chip processing">处理中</span></td>
                                    <td>
                                        <div>2024-07-30</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">14:30</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">打印</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: var(--primary-color);">P2024073002</div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 500;">李四</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">139****1234</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <span class="material-icons-round" style="font-size: 16px; color: var(--text-secondary);">description</span>
                                            <span>1个文件</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 600;">¥12.50</div>
                                    </td>
                                    <td><span class="status-chip printing">打印中</span></td>
                                    <td>
                                        <div>2024-07-30</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">14:25</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">暂停</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: var(--primary-color);">P2024073003</div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 500;">王五</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">137****9876</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <span class="material-icons-round" style="font-size: 16px; color: var(--text-secondary);">description</span>
                                            <span>2个文件</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 600;">¥28.90</div>
                                    </td>
                                    <td><span class="status-chip completed">已完成</span></td>
                                    <td>
                                        <div>2024-07-30</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">14:20</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                            <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">发货</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: 600; color: var(--primary-color);">P2024073004</div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 500;">赵六</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">136****5432</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <span class="material-icons-round" style="font-size: 16px; color: var(--text-secondary);">description</span>
                                            <span>5个文件</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-weight: 600;">¥67.20</div>
                                    </td>
                                    <td><span class="status-chip pending">待处理</span></td>
                                    <td>
                                        <div>2024-07-30</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">14:15</div>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">开始</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 现代化交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 导航菜单交互
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    // 更新页面标题和面包屑
                    const title = this.querySelector('span:last-child').textContent;
                    document.querySelector('.page-title').textContent = title;
                    document.querySelector('.breadcrumb-item:last-child span').textContent = title;

                    // 添加页面切换动画
                    const mainContent = document.querySelector('.main-content');
                    mainContent.style.opacity = '0.7';
                    setTimeout(() => {
                        mainContent.style.opacity = '1';
                    }, 150);
                });
            });

            // 搜索功能增强
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    const rows = document.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                            row.style.animation = 'fadeInUp 0.3s ease-out';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }

            // 统计卡片动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease-out';
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.stat-card, .card').forEach(card => {
                observer.observe(card);
            });

            // 按钮点击效果
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // 创建波纹效果
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // 实时时间更新
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // 可以在这里添加时间显示元素
                // document.querySelector('.current-time').textContent = timeString;
            }

            setInterval(updateTime, 1000);
            updateTime();

            // 响应式菜单
            window.toggleSidebar = function() {
                document.querySelector('.sidebar').classList.toggle('open');
            };

            // 模拟数据更新
            function simulateDataUpdate() {
                const statNumbers = document.querySelectorAll('.stat-number');
                statNumbers.forEach(num => {
                    const currentValue = parseInt(num.textContent);
                    const change = Math.floor(Math.random() * 10) - 5; // -5 到 +5 的随机变化
                    const newValue = Math.max(0, currentValue + change);

                    if (change !== 0) {
                        num.style.animation = 'pulse 0.5s ease-in-out';
                        setTimeout(() => {
                            num.textContent = newValue;
                            num.style.animation = '';
                        }, 250);
                    }
                });
            }

            // 每30秒模拟数据更新
            setInterval(simulateDataUpdate, 30000);
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
